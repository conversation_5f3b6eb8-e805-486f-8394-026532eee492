import { type NextRequest, NextResponse } from 'next/server';
import * as Sentry from '@sentry/nextjs';
import { authMiddleware } from './lib/auth/middleware-auth';
import { rateLimitMiddleware } from './lib/rate-limit';
import { applySecurityHeaders, detectSecurityThreats, logSecurityEvent } from './lib/security/headers';

// Middleware context interface
interface MiddlewareContext {
  requestId: string;
  startTime: number;
  userAgent: string;
  ip: string;
  path: string;
}

// Main middleware function with comprehensive security and monitoring
export async function middleware(request: NextRequest) {
  const startTime = Date.now();
  const requestId = crypto.randomUUID();
  
  // Create middleware context
  const context: MiddlewareContext = {
    requestId,
    startTime,
    userAgent: request.headers.get('user-agent') || '',
    ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
    path: request.nextUrl.pathname,
  };

  try {
    // Set up Sentry context for this request
    Sentry.setContext('request', {
      id: requestId,
      url: request.url,
      method: request.method,
      userAgent: context.userAgent,
      ip: context.ip,
      headers: Object.fromEntries(
        Array.from(request.headers.entries())
          .filter(([key]) => !['authorization', 'cookie', 'x-api-key'].includes(key.toLowerCase()))
      ),
    });

    // 1. Security threat detection (first line of defense)
    const securityThreats = detectSecurityThreats(request);
    if (securityThreats.length > 0) {
      securityThreats.forEach(threat => logSecurityEvent(request, threat));
      
      // Block high severity threats
      const criticalThreats = securityThreats.filter(t => t.severity === 'critical' || t.severity === 'high');
      if (criticalThreats.length > 0) {
        return createBlockedResponse('Security threat detected', 403, context);
      }
    }

    // 2. Rate limiting (second line of defense)
    const rateLimitResult = await rateLimitMiddleware(request);
    if (rateLimitResult.blocked && rateLimitResult.response) {
      // Add security headers to rate limit response
      const response = applySecurityHeaders(rateLimitResult.response);
      addMiddlewareHeaders(response, context, Date.now() - startTime);
      return response;
    }

    // 3. Authentication middleware (existing logic)
    const { response: authResponse } = await authMiddleware(request);
    
    // 4. Apply security headers to the response
    const secureResponse = applySecurityHeaders(authResponse);
    
    // 5. Add rate limiting headers if available
    if (rateLimitResult.headers) {
      Object.entries(rateLimitResult.headers).forEach(([key, value]) => {
        secureResponse.headers.set(key, value);
      });
    }
    
    // 6. Add middleware processing headers
    addMiddlewareHeaders(secureResponse, context, Date.now() - startTime);
    
    // 7. Add pathname header (existing functionality)
    secureResponse.headers.set('x-pathname', request.nextUrl.pathname);
    
    return secureResponse;
    
  } catch (error) {
    // Log middleware errors to Sentry
    console.error('Middleware error:', error);
    Sentry.captureException(error, {
      tags: {
        component: 'middleware',
        operation: 'process_request',
      },
      extra: {
        context,
        processingTime: Date.now() - startTime,
      },
    });
    
    // Fail open - allow request to continue with basic security headers
    const response = NextResponse.next();
    const secureResponse = applySecurityHeaders(response);
    addMiddlewareHeaders(secureResponse, context, Date.now() - startTime, 'error');
    
    return secureResponse;
  }
}

// Helper function to create blocked responses
function createBlockedResponse(
  reason: string, 
  status: number, 
  context: MiddlewareContext
): NextResponse {
  const response = new NextResponse(
    JSON.stringify({
      error: 'Request Blocked',
      message: reason,
      requestId: context.requestId,
      timestamp: new Date().toISOString(),
    }),
    {
      status,
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  
  const secureResponse = applySecurityHeaders(response);
  addMiddlewareHeaders(secureResponse, context, Date.now() - context.startTime, 'blocked');
  
  return secureResponse;
}

// Helper function to add middleware processing headers
function addMiddlewareHeaders(
  response: NextResponse, 
  context: MiddlewareContext, 
  processingTime: number,
  status: 'success' | 'error' | 'blocked' = 'success'
): void {
  response.headers.set('X-Request-ID', context.requestId);
  response.headers.set('X-Processing-Time', processingTime.toString());
  response.headers.set('X-Middleware-Status', status);
  response.headers.set('X-Middleware-Version', '1.0.0');
  
  // Add performance timing headers for monitoring
  if (processingTime > 1000) { // Log slow middleware processing
    console.warn(`Slow middleware processing: ${processingTime}ms for ${context.path}`);
    Sentry.addBreadcrumb({
      category: 'performance',
      message: 'Slow middleware processing',
      data: {
        processingTime,
        path: context.path,
        requestId: context.requestId,
      },
      level: 'warning',
    });
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - _vercel (Vercel internals)
     * - sitemap.xml, robots.txt, manifest.json (metadata files)
     * - monitoring (Sentry tunnel)
     * Note: API routes are now included for rate limiting
     */
    '/((?!_next/static|_next/image|favicon.ico|_vercel|sitemap.xml|robots.txt|manifest.json|monitoring).*)',
  ],
};
