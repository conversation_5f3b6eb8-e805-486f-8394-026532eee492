import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';
import { NextRequest, NextResponse } from 'next/server';
import * as Sentry from '@sentry/nextjs';

// Configuration interface for rate limiting
export interface RateLimitConfig {
  identifier: 'ip' | 'user' | 'session';
  limit: number;
  window: string; // Examples: '1m', '15m', '1h', '1d'
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyPrefix?: string;
}

// Rate limit decision result
export interface RateLimitDecision {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: Date;
  retryAfter?: number;
  reason?: string;
}

// Rate limiting engine class
export class RateLimitEngine {
  private redis: Redis | null = null;
  private ratelimiters: Map<string, Ratelimit> = new Map();

  constructor() {
    // Initialize Redis only if credentials are available
    if (process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN) {
      this.redis = new Redis({
        url: process.env.UPSTASH_REDIS_REST_URL,
        token: process.env.UPSTASH_REDIS_REST_TOKEN,
      });
    } else {
      console.warn('Redis credentials not found. Rate limiting will be disabled.');
    }
  }

  private getRatelimiter(config: RateLimitConfig): Ratelimit | null {
    if (!this.redis) return null;

    const key = `${config.keyPrefix || 'default'}_${config.limit}_${config.window}`;
    
    if (!this.ratelimiters.has(key)) {
      const ratelimiter = new Ratelimit({
        redis: this.redis,
        limiter: Ratelimit.slidingWindow(config.limit, config.window as any),
        analytics: true,
        prefix: config.keyPrefix || 'rl',
      });
      
      this.ratelimiters.set(key, ratelimiter);
    }
    
    return this.ratelimiters.get(key) || null;
  }

  public getIdentifier(request: NextRequest, type: 'ip' | 'user' | 'session'): string {
    switch (type) {
      case 'ip':
        return this.getClientIP(request);
      case 'user':
        // Extract user ID from session or auth header
        const userId = request.headers.get('x-user-id') || request.cookies.get('user-id')?.value;
        return userId || this.getClientIP(request);
      case 'session':
        const sessionId = request.cookies.get('session-id')?.value || request.headers.get('x-session-id');
        return sessionId || this.getClientIP(request);
      default:
        return this.getClientIP(request);
    }
  }

  private getClientIP(request: NextRequest): string {
    // Try to get real IP from various headers
    const xForwardedFor = request.headers.get('x-forwarded-for');
    const xRealIP = request.headers.get('x-real-ip');
    const cfConnectingIP = request.headers.get('cf-connecting-ip');
    
    if (xForwardedFor) {
      return xForwardedFor.split(',')[0].trim();
    }
    
    if (xRealIP) {
      return xRealIP;
    }
    
    if (cfConnectingIP) {
      return cfConnectingIP;
    }
    
    // Fallback to a default IP for localhost
    return '127.0.0.1';
  }

  public shouldBypass(request: NextRequest): boolean {
    // Bypass rate limiting for health checks and monitoring
    const bypassPaths = [
      '/api/health',
      '/api/status',
      '/monitoring', // Sentry tunnel
      '/_next/static',
      '/_next/image',
      '/favicon.ico',
      '/sitemap.xml',
      '/robots.txt',
    ];

    return bypassPaths.some(path => request.nextUrl.pathname.startsWith(path));
  }

  public async evaluate(
    request: NextRequest,
    config: RateLimitConfig
  ): Promise<RateLimitDecision> {
    // Check if rate limiting is disabled
    if (process.env.RATE_LIMIT_ENABLED !== 'true') {
      return {
        allowed: true,
        limit: config.limit,
        remaining: config.limit,
        resetTime: new Date(Date.now() + 60000), // 1 minute from now
      };
    }

    // Check if we should bypass rate limiting
    if (this.shouldBypass(request)) {
      return {
        allowed: true,
        limit: config.limit,
        remaining: config.limit,
        resetTime: new Date(Date.now() + 60000),
        reason: 'bypassed',
      };
    }

    const ratelimiter = this.getRatelimiter(config);
    
    if (!ratelimiter) {
      // If Redis is not available, allow the request but log the issue
      console.warn('Rate limiting unavailable - Redis not configured');
      return {
        allowed: true,
        limit: config.limit,
        remaining: config.limit,
        resetTime: new Date(Date.now() + 60000),
        reason: 'redis_unavailable',
      };
    }

    try {
      const identifier = this.getIdentifier(request, config.identifier);
      const result = await ratelimiter.limit(identifier);

      const decision: RateLimitDecision = {
        allowed: result.success,
        limit: result.limit,
        remaining: result.remaining,
        resetTime: new Date(result.reset),
      };

      if (!result.success) {
        decision.retryAfter = Math.round((result.reset - Date.now()) / 1000);
        decision.reason = 'rate_limit_exceeded';
        
        // Log rate limit violation to Sentry
        Sentry.addBreadcrumb({
          category: 'rate-limit',
          message: 'Rate limit exceeded',
          data: {
            identifier,
            limit: result.limit,
            remaining: result.remaining,
            path: request.nextUrl.pathname,
          },
          level: 'warning',
        });
      }

      return decision;
    } catch (error) {
      // If rate limiting fails, allow the request but log the error
      console.error('Rate limiting error:', error);
      Sentry.captureException(error, {
        tags: {
          component: 'rate-limit',
          operation: 'evaluate',
        },
      });

      return {
        allowed: true,
        limit: config.limit,
        remaining: config.limit,
        resetTime: new Date(Date.now() + 60000),
        reason: 'rate_limit_error',
      };
    }
  }
}

// Default rate limiting configurations
export const RATE_LIMIT_CONFIGS = {
  // General API endpoints
  api: {
    identifier: 'ip' as const,
    limit: parseInt(process.env.RATE_LIMIT_REQUESTS_PER_MINUTE || '100'),
    window: '1m',
    keyPrefix: 'api',
  },
  
  // Authentication endpoints (stricter)
  auth: {
    identifier: 'ip' as const,
    limit: parseInt(process.env.RATE_LIMIT_AUTH_ATTEMPTS || '5'),
    window: '15m',
    keyPrefix: 'auth',
  },
  
  // Form submissions
  forms: {
    identifier: 'ip' as const,
    limit: 10,
    window: '5m',
    keyPrefix: 'forms',
  },
  
  // File uploads
  uploads: {
    identifier: 'ip' as const,
    limit: 5,
    window: '10m',
    keyPrefix: 'uploads',
  },
  
  // Password reset attempts
  passwordReset: {
    identifier: 'ip' as const,
    limit: 3,
    window: '1h',
    keyPrefix: 'pwd_reset',
  },
} satisfies Record<string, RateLimitConfig>;

// Singleton instance
export const rateLimitEngine = new RateLimitEngine();

// Helper function to get rate limit configuration based on request path
export function getRateLimitConfig(pathname: string): RateLimitConfig {
  if (pathname.startsWith('/api/auth') || pathname.startsWith('/auth')) {
    return RATE_LIMIT_CONFIGS.auth;
  }
  
  if (pathname.includes('/forgot-password') || pathname.includes('/reset-password')) {
    return RATE_LIMIT_CONFIGS.passwordReset;
  }
  
  if (pathname.startsWith('/api/upload')) {
    return RATE_LIMIT_CONFIGS.uploads;
  }
  
  if (pathname.startsWith('/api/')) {
    return RATE_LIMIT_CONFIGS.api;
  }
  
  // Default configuration for forms and other endpoints
  return RATE_LIMIT_CONFIGS.forms;
}

// Middleware function for rate limiting
export async function rateLimitMiddleware(request: NextRequest): Promise<{
  blocked: boolean;
  response?: NextResponse;
  headers?: Record<string, string>;
}> {
  try {
    const config = getRateLimitConfig(request.nextUrl.pathname);
    const decision = await rateLimitEngine.evaluate(request, config);
    
    const rateLimitHeaders = {
      'X-RateLimit-Limit': decision.limit.toString(),
      'X-RateLimit-Remaining': decision.remaining.toString(),
      'X-RateLimit-Reset': decision.resetTime.getTime().toString(),
    };
    
    if (!decision.allowed) {
      const response = new NextResponse(
        JSON.stringify({
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: decision.retryAfter,
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': decision.retryAfter?.toString() || '60',
            ...rateLimitHeaders,
          },
        }
      );
      
      return { blocked: true, response };
    }
    
    return { blocked: false, headers: rateLimitHeaders };
  } catch (error) {
    // If rate limiting fails, allow the request
    console.error('Rate limiting middleware error:', error);
    Sentry.captureException(error, {
      tags: {
        component: 'rate-limit-middleware',
      },
    });
    
    return { blocked: false };
  }
}