// This file configures the initialization of Sentry for edge features (middleware, edge routes, and so on).
// The config you add here will be used whenever one of the edge features is loaded.
// Note that this config is unrelated to the Vercel Edge Runtime and is also required when running locally.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

// Environment-based configuration for edge runtime
const isProduction = process.env.NODE_ENV === 'production';
const isDevelopment = process.env.NODE_ENV === 'development';

Sentry.init({
  dsn: "https://<EMAIL>/4509470542594128",

  // Environment and release tracking
  environment: process.env.NODE_ENV || 'development',
  release: process.env.VERCEL_GIT_COMMIT_SHA || process.env.npm_package_version || 'unknown',

  // Lower sample rates for edge runtime to reduce overhead
  tracesSampleRate: isDevelopment ? 1.0 : 0.05, // 100% in dev, 5% in production

  // Disable session replay in edge runtime (not supported)
  replaysSessionSampleRate: 0,
  replaysOnErrorSampleRate: 0,

  // Enable logs but with filtering
  enableLogs: true,

  // Debug mode - only in development
  debug: isDevelopment,

  // Edge-specific error filtering
  beforeSend(event, hint) {
    const error = hint.originalException;
    
    if (error instanceof Error) {
      // Skip middleware timeout errors (common in edge runtime)
      if (error.message.includes('timeout') || error.message.includes('Middleware')) {
        return null;
      }
      
      // Skip edge runtime specific non-critical errors
      if (error.message.includes('edge-runtime') || error.message.includes('AbortController')) {
        return null;
      }
    }
    
    // Add edge runtime context
    if (event.contexts) {
      event.contexts.edge = {
        runtime: 'edge',
        timestamp: new Date().toISOString(),
      };
    }
    
    // Add middleware-specific tags for better filtering
    if (event.tags) {
      event.tags.runtime = 'edge';
      event.tags.component = 'middleware';
    }
    
    return event;
  },

  // Minimal integrations for edge runtime
  integrations: [
    // Only use integrations that work in edge runtime
    Sentry.httpIntegration({
      tracing: {
        shouldCreateSpanForRequest: (url) => {
          // Be very selective about what we trace in edge runtime
          return !url.includes('/_next/') && 
                 !url.includes('/favicon.ico') &&
                 !url.includes('/api/health');
        },
      },
    }),
  ],

  // Custom tags for edge runtime
  initialScope: {
    tags: {
      component: 'edge',
      runtime: 'edge',
      version: process.env.npm_package_version || 'unknown',
    },
  },

  // Conservative sampling for edge runtime
  tracesSampler: (samplingContext) => {
    // Sample middleware operations less frequently to reduce overhead
    if (samplingContext.name?.includes('middleware')) {
      return isProduction ? 0.01 : 0.1; // Very low sampling for middleware
    }
    
    // Sample API routes moderately
    if (samplingContext.request?.url?.includes('/api/')) {
      return isProduction ? 0.05 : 0.5;
    }
    
    // Default sampling
    return isProduction ? 0.02 : 0.1;
  },

  // Optimize for edge runtime performance
  maxBreadcrumbs: 20, // Reduced from default 100
  attachStacktrace: false, // Disable to reduce payload size
});

// Set edge-specific context
Sentry.setUser({
  id: 'edge-runtime',
  username: 'system',
});

Sentry.setTags({
  runtime: 'edge',
  deployment: process.env.VERCEL_ENV || 'local',
  region: process.env.VERCEL_REGION || 'unknown',
});
