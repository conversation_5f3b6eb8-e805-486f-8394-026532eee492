// This file configures the initialization of Sentry on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

// Environment-based configuration
const isProduction = process.env.NODE_ENV === 'production';
const isDevelopment = process.env.NODE_ENV === 'development';

Sentry.init({
  dsn: "https://<EMAIL>/4509470542594128",

  // Environment and release tracking
  environment: process.env.NODE_ENV || 'development',
  release: process.env.VERCEL_GIT_COMMIT_SHA || process.env.npm_package_version || 'unknown',

  // Performance monitoring - adjusted for environment
  tracesSampleRate: isDevelopment ? 1.0 : 0.1, // 100% in dev, 10% in production
  
  // Session replay - only in production and with lower sample rates
  replaysSessionSampleRate: isProduction ? 0.01 : 0, // 1% in production, off in dev
  replaysOnErrorSampleRate: isProduction ? 0.1 : 0, // 10% on errors in production

  // Enable logs to be sent to Sentry
  enableLogs: true,
  
  // Debug mode - only in development
  debug: isDevelopment,

  // Improved error filtering
  beforeSend(event, hint) {
    // Filter out common non-critical errors
    const error = hint.originalException;
    
    if (error instanceof Error) {
      // Skip CORS errors
      if (error.message.includes('CORS') || error.message.includes('fetch')) {
        return null;
      }
      
      // Skip network errors that aren't actionable
      if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
        return null;
      }
      
      // Skip client-side hydration mismatches in development
      if (isDevelopment && error.message.includes('Hydration')) {
        return null;
      }
    }
    
    // Add server context
    if (event.contexts) {
      event.contexts.server = {
        runtime: 'node',
        version: process.version,
        platform: process.platform,
        memory_usage: process.memoryUsage(),
      };
    }
    
    return event;
  },

  // Enhanced integrations
  integrations: [
    // Use default integrations
  ],

  // Custom tags for better filtering
  initialScope: {
    tags: {
      component: 'server',
      version: process.env.npm_package_version || 'unknown',
    },
  },

  // Performance thresholds
  tracesSampler: (samplingContext) => {
    // Always sample errors
    if (samplingContext.request?.url?.includes('/api/')) {
      return isProduction ? 0.1 : 1.0;
    }
    
    // Sample page loads less frequently
    return isProduction ? 0.05 : 1.0;
  },
});

// Set user context for server-side operations
Sentry.setUser({
  id: 'server',
  username: 'system',
});

// Add global tags
Sentry.setTags({
  runtime: 'server',
  deployment: process.env.VERCEL_ENV || 'local',
});
