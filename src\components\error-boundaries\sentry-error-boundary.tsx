'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import * as Sentry from '@sentry/nextjs';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Error boundary props interface
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  enableRetry?: boolean;
  enableReporting?: boolean;
  level?: 'page' | 'component' | 'widget';
}

// Error boundary state interface
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
  retryCount: number;
}

// Enhanced error boundary with Sentry integration
export class SentryErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private maxRetries = 3;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error boundary caught an error:', error, errorInfo);
    
    // Capture error with Sentry
    const errorId = Sentry.captureException(error, {
      contexts: {
        react: {
          componentStack: errorInfo.componentStack,
        },
      },
      tags: {
        errorBoundary: true,
        level: this.props.level || 'component',
      },
      extra: {
        errorInfo,
        retryCount: this.state.retryCount,
      },
    });

    this.setState({
      error,
      errorInfo,
      errorId,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null,
        retryCount: prevState.retryCount + 1,
      }));
    }
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleReportProblem = () => {
    if (this.state.errorId) {
      // Open Sentry feedback dialog
      Sentry.showReportDialog({
        eventId: this.state.errorId,
        title: 'Bir hata oluştu',
        subtitle: 'Bu hatayı bildirmek için aşağıdaki formu doldurabilirsiniz.',
        subtitle2: 'Hata ID: ' + this.state.errorId,
        labelName: 'İsim',
        labelEmail: 'E-posta',
        labelComments: 'Hatayı açıklayın',
        labelSubmit: 'Gönder',
        labelClose: 'Kapat',
        successMessage: 'Geri bildiriminiz gönderildi. Teşekkürler!',
      });
    }
  };

  override render() {
    if (this.state.hasError) {
      // Custom fallback UI if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI based on level
      return this.renderErrorUI();
    }

    return this.props.children;
  }

  private renderErrorUI() {
    const { level = 'component', showDetails = false, enableRetry = true, enableReporting = true } = this.props;
    const { error, retryCount, errorId } = this.state;
    const canRetry = enableRetry && retryCount < this.maxRetries;

    // Page-level error (full screen)
    if (level === 'page') {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 w-12 h-12 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center">
                <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <CardTitle className="text-xl">Beklenmedik Bir Hata Oluştu</CardTitle>
              <CardDescription>
                Sayfayı yüklerken bir sorun yaşandı. Lütfen tekrar deneyin.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {showDetails && error && (
                <Alert variant="destructive">
                  <Bug className="h-4 w-4" />
                  <AlertDescription className="font-mono text-xs">
                    {error.message}
                  </AlertDescription>
                </Alert>
              )}
              
              {errorId && (
                <div className="text-xs text-gray-500 text-center">
                  Hata ID: {errorId}
                </div>
              )}

              <div className="flex flex-col gap-2">
                {canRetry && (
                  <Button onClick={this.handleRetry} className="w-full">
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Tekrar Dene ({this.maxRetries - retryCount} deneme kaldı)
                  </Button>
                )}
                
                <Button variant="outline" onClick={this.handleGoHome} className="w-full">
                  <Home className="w-4 h-4 mr-2" />
                  Ana Sayfaya Dön
                </Button>
                
                {enableReporting && errorId && (
                  <Button variant="ghost" onClick={this.handleReportProblem} className="w-full">
                    <Bug className="w-4 h-4 mr-2" />
                    Hatayı Bildir
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    // Component-level error (inline)
    if (level === 'component') {
      return (
        <Card className="border-red-200 dark:border-red-800">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">
                  Bileşen yüklenemedi
                </h3>
                <p className="text-xs text-red-700 dark:text-red-300 mb-3">
                  Bu bölümde bir hata oluştu. Sayfayı yenilemeyi deneyin.
                </p>
                
                {showDetails && error && (
                  <div className="mb-3 p-2 bg-red-50 dark:bg-red-900/20 rounded text-xs font-mono text-red-800 dark:text-red-200">
                    {error.message}
                  </div>
                )}
                
                <div className="flex gap-2">
                  {canRetry && (
                    <Button size="sm" variant="outline" onClick={this.handleRetry}>
                      <RefreshCw className="w-3 h-3 mr-1" />
                      Tekrar Dene
                    </Button>
                  )}
                  
                  {enableReporting && errorId && (
                    <Button size="sm" variant="ghost" onClick={this.handleReportProblem}>
                      <Bug className="w-3 h-3 mr-1" />
                      Bildir
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    // Widget-level error (minimal)
    return (
      <div className="p-3 border border-red-200 dark:border-red-800 rounded-md bg-red-50 dark:bg-red-900/10">
        <div className="flex items-center gap-2">
          <AlertTriangle className="w-4 h-4 text-red-600 dark:text-red-400" />
          <span className="text-sm text-red-800 dark:text-red-200">
            Widget yüklenemedi
          </span>
          {canRetry && (
            <Button size="sm" variant="ghost" onClick={this.handleRetry} className="ml-auto h-6 px-2">
              <RefreshCw className="w-3 h-3" />
            </Button>
          )}
        </div>
      </div>
    );
  }
}

// HOC for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => {
    return (
      <SentryErrorBoundary {...errorBoundaryProps}>
        <Component {...props} />
      </SentryErrorBoundary>
    );
  };

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// Specialized error boundaries for different use cases
export const PageErrorBoundary = ({ children }: { children: ReactNode }) => (
  <SentryErrorBoundary level="page" showDetails={false} enableRetry={true} enableReporting={true}>
    {children}
  </SentryErrorBoundary>
);

export const ComponentErrorBoundary = ({ children }: { children: ReactNode }) => (
  <SentryErrorBoundary level="component" showDetails={false} enableRetry={true} enableReporting={true}>
    {children}
  </SentryErrorBoundary>
);

export const WidgetErrorBoundary = ({ children }: { children: ReactNode }) => (
  <SentryErrorBoundary level="widget" showDetails={false} enableRetry={true} enableReporting={false}>
    {children}
  </SentryErrorBoundary>
);

// Hook for programmatic error reporting
export function useErrorReporting() {
  const reportError = React.useCallback((error: Error, context?: Record<string, any>) => {
    const errorId = Sentry.captureException(error, {
      tags: {
        source: 'manual_report',
      },
      extra: {
        context,
        timestamp: new Date().toISOString(),
      },
    });
    
    return errorId;
  }, []);

  const reportMessage = React.useCallback((message: string, level: 'info' | 'warning' | 'error' = 'error') => {
    const eventId = Sentry.captureMessage(message, level);
    return eventId;
  }, []);

  return {
    reportError,
    reportMessage,
  };
}

// Global unhandled error handler setup
export function setupGlobalErrorHandlers() {
  // Handle unhandled promise rejections
  if (typeof window !== 'undefined') {
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      
      Sentry.captureException(event.reason, {
        tags: {
          source: 'unhandledrejection',
        },
        extra: {
          promise: event.promise,
        },
      });
    });

    // Handle uncaught errors
    window.addEventListener('error', (event) => {
      console.error('Uncaught error:', event.error);
      
      Sentry.captureException(event.error, {
        tags: {
          source: 'uncaught_error',
        },
        extra: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        },
      });
    });
  }
}

// Performance monitoring helpers
export function withPerformanceMonitoring<T extends (...args: any[]) => any>(
  fn: T,
  operationName: string
): T {
  return ((...args: any[]) => {
    // Use the new span API instead of startTransaction
    return Sentry.startSpan({ name: operationName, op: 'function' }, () => {
      try {
        const result = fn(...args);

        // Handle async functions
        if (result instanceof Promise) {
          return result.catch((error) => {
            Sentry.captureException(error);
            throw error;
          });
        }

        // Handle sync functions
        return result;
      } catch (error) {
        Sentry.captureException(error);
        throw error;
      }
    });
  }) as T;
}