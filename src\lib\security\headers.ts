import { NextRequest, NextResponse } from 'next/server';
import * as Sentry from '@sentry/nextjs';

// Security headers configuration interface
export interface SecurityHeadersConfig {
  contentSecurityPolicy?: string;
  frameOptions?: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM';
  contentTypeOptions?: boolean;
  referrerPolicy?: string;
  xssProtection?: string;
  permissionsPolicy?: string;
  strictTransportSecurity?: string;
  crossOriginEmbedderPolicy?: string;
  crossOriginOpenerPolicy?: string;
  crossOriginResourcePolicy?: string;
}

// Default security configuration
export const DEFAULT_SECURITY_CONFIG: SecurityHeadersConfig = {
  frameOptions: 'DENY',
  contentTypeOptions: true,
  referrerPolicy: 'strict-origin-when-cross-origin',
  xssProtection: '1; mode=block',
  permissionsPolicy: 'camera=(), microphone=(), geolocation=(), payment=()',
  crossOriginEmbedderPolicy: 'require-corp',
  crossOriginOpenerPolicy: 'same-origin',
  crossOriginResourcePolicy: 'same-site',
};

// Environment-specific security configurations
function getEnvironmentSecurityConfig(): SecurityHeadersConfig {
  const isDev = process.env.NODE_ENV === 'development';
  const isProduction = process.env.NODE_ENV === 'production';
  
  // Get Supabase hostname for CSP
  function getSupabaseHostname(): string {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    if (!supabaseUrl) {
      return 'localhost';
    }
    
    try {
      const url = new URL(supabaseUrl);
      return url.hostname;
    } catch {
      return 'localhost';
    }
  }
  
  const supabaseHostname = getSupabaseHostname();
  
  const config: SecurityHeadersConfig = {
    ...DEFAULT_SECURITY_CONFIG,
    
    // Content Security Policy
    contentSecurityPolicy: [
      "default-src 'self'",
      isDev
        ? "script-src 'self' 'unsafe-eval' 'unsafe-inline'"
        : "script-src 'self' 'unsafe-inline' https://vercel.live https://browser.sentry-cdn.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      `img-src 'self' data: blob: https://${supabaseHostname} https://lh3.googleusercontent.com https://images.unsplash.com`,
      "font-src 'self' data: https://fonts.gstatic.com",
      `connect-src 'self' https://${supabaseHostname} wss://${supabaseHostname} https://vitals.vercel-insights.com ${isDev ? 'ws://localhost:*' : ''} https://browser.sentry-cdn.com https://o4507988331003904.ingest.de.sentry.io`,
      "media-src 'self' data: blob:",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
      isDev ? '' : 'upgrade-insecure-requests',
    ]
      .filter(Boolean)
      .join('; '),
    
    // HSTS only in production
    strictTransportSecurity: isProduction 
      ? 'max-age=31536000; includeSubDomains; preload' 
      : undefined,
  };
  
  return config;
}

// Security headers middleware class
export class SecurityHeadersMiddleware {
  private config: SecurityHeadersConfig;
  
  constructor(config?: SecurityHeadersConfig) {
    this.config = { ...getEnvironmentSecurityConfig(), ...config };
  }
  
  public addSecurityHeaders(response: NextResponse): NextResponse {
    try {
      // X-Frame-Options
      if (this.config.frameOptions) {
        response.headers.set('X-Frame-Options', this.config.frameOptions);
      }
      
      // X-Content-Type-Options
      if (this.config.contentTypeOptions) {
        response.headers.set('X-Content-Type-Options', 'nosniff');
      }
      
      // Referrer-Policy
      if (this.config.referrerPolicy) {
        response.headers.set('Referrer-Policy', this.config.referrerPolicy);
      }
      
      // X-XSS-Protection
      if (this.config.xssProtection) {
        response.headers.set('X-XSS-Protection', this.config.xssProtection);
      }
      
      // Permissions-Policy
      if (this.config.permissionsPolicy) {
        response.headers.set('Permissions-Policy', this.config.permissionsPolicy);
      }
      
      // Strict-Transport-Security (HSTS)
      if (this.config.strictTransportSecurity) {
        response.headers.set('Strict-Transport-Security', this.config.strictTransportSecurity);
      }
      
      // Content-Security-Policy
      if (this.config.contentSecurityPolicy) {
        response.headers.set('Content-Security-Policy', this.config.contentSecurityPolicy);
      }
      
      // Cross-Origin headers
      if (this.config.crossOriginEmbedderPolicy) {
        response.headers.set('Cross-Origin-Embedder-Policy', this.config.crossOriginEmbedderPolicy);
      }
      
      if (this.config.crossOriginOpenerPolicy) {
        response.headers.set('Cross-Origin-Opener-Policy', this.config.crossOriginOpenerPolicy);
      }
      
      if (this.config.crossOriginResourcePolicy) {
        response.headers.set('Cross-Origin-Resource-Policy', this.config.crossOriginResourcePolicy);
      }
      
      // Security-related custom headers
      response.headers.set('X-Powered-By', ''); // Remove X-Powered-By header
      response.headers.set('Server', ''); // Remove Server header
      
      return response;
    } catch (error) {
      console.error('Security headers middleware error:', error);
      Sentry.captureException(error, {
        tags: {
          component: 'security-headers-middleware',
        },
      });
      
      return response;
    }
  }
  
  public updateConfig(newConfig: Partial<SecurityHeadersConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// Singleton instance
export const securityHeadersMiddleware = new SecurityHeadersMiddleware();

// Helper function to apply security headers to a response
export function applySecurityHeaders(
  response: NextResponse,
  config?: SecurityHeadersConfig
): NextResponse {
  if (config) {
    const middleware = new SecurityHeadersMiddleware(config);
    return middleware.addSecurityHeaders(response);
  }
  
  return securityHeadersMiddleware.addSecurityHeaders(response);
}

// Helper function to create a response with security headers
export function createSecureResponse(
  body?: BodyInit | null,
  init?: ResponseInit,
  securityConfig?: SecurityHeadersConfig
): NextResponse {
  const response = new NextResponse(body, init);
  return applySecurityHeaders(response, securityConfig);
}

// Security event logging
export interface SecurityEvent {
  type: 'csp_violation' | 'xss_attempt' | 'frame_denied' | 'mixed_content';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userAgent?: string;
  ip?: string;
  path?: string;
  details?: Record<string, any>;
}

export function logSecurityEvent(request: NextRequest, event: SecurityEvent): void {
  const securityData = {
    ...event,
    timestamp: new Date().toISOString(),
    userAgent: event.userAgent || request.headers.get('user-agent'),
    ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || request.ip,
    path: event.path || request.nextUrl.pathname,
    referer: request.headers.get('referer'),
  };
  
  // Log to console for development
  if (process.env.NODE_ENV === 'development') {
    console.warn('Security Event:', securityData);
  }
  
  // Send to Sentry with appropriate level
  const sentryLevel = event.severity === 'critical' ? 'error' : 
                     event.severity === 'high' ? 'warning' : 'info';
  
  Sentry.addBreadcrumb({
    category: 'security',
    message: `Security event: ${event.type}`,
    data: securityData,
    level: sentryLevel,
  });
  
  if (event.severity === 'high' || event.severity === 'critical') {
    Sentry.captureMessage(`Security Event: ${event.type}`, sentryLevel);
  }
}

// Content Security Policy violation reporting endpoint helper
export function handleCSPViolation(request: NextRequest): NextResponse {
  try {
    // Log CSP violation
    logSecurityEvent(request, {
      type: 'csp_violation',
      severity: 'medium',
      details: {
        endpoint: '/api/csp-report',
        method: request.method,
      },
    });
    
    return new NextResponse('CSP violation reported', { status: 200 });
  } catch (error) {
    console.error('CSP violation handler error:', error);
    return new NextResponse('Error processing CSP report', { status: 500 });
  }
}

// Middleware function to check for security threats
export function detectSecurityThreats(request: NextRequest): SecurityEvent[] {
  const threats: SecurityEvent[] = [];
  const userAgent = request.headers.get('user-agent') || '';
  const path = request.nextUrl.pathname;
  
  // Detect potential XSS attempts in URL parameters
  const searchParams = request.nextUrl.searchParams;
  for (const [key, value] of searchParams.entries()) {
    if (containsXSSPattern(value)) {
      threats.push({
        type: 'xss_attempt',
        severity: 'high',
        details: {
          parameter: key,
          value: value.substring(0, 100), // Limit logged value length
        },
      });
    }
  }
  
  // Detect suspicious user agents
  if (containsSuspiciousUserAgent(userAgent)) {
    threats.push({
      type: 'frame_denied',
      severity: 'medium',
      details: {
        userAgent: userAgent.substring(0, 200),
        reason: 'suspicious_user_agent',
      },
    });
  }
  
  // Detect directory traversal attempts
  if (path.includes('../') || path.includes('..\\')) {
    threats.push({
      type: 'xss_attempt',
      severity: 'high',
      details: {
        path,
        reason: 'directory_traversal_attempt',
      },
    });
  }
  
  return threats;
}

// Helper functions for threat detection
function containsXSSPattern(value: string): boolean {
  const xssPatterns = [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<iframe[^>]*>/gi,
    /<object[^>]*>/gi,
    /<embed[^>]*>/gi,
  ];
  
  return xssPatterns.some(pattern => pattern.test(value));
}

function containsSuspiciousUserAgent(userAgent: string): boolean {
  const suspiciousPatterns = [
    /curl/i,
    /wget/i,
    /python/i,
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
  ];
  
  // Only flag as suspicious if it's not a legitimate bot
  const legitimateBots = [
    /googlebot/i,
    /bingbot/i,
    /slurp/i,
    /duckduckbot/i,
    /baiduspider/i,
    /yandexbot/i,
    /facebookexternalhit/i,
    /twitterbot/i,
    /linkedinbot/i,
  ];
  
  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(userAgent));
  const isLegitimate = legitimateBots.some(pattern => pattern.test(userAgent));
  
  return isSuspicious && !isLegitimate;
}